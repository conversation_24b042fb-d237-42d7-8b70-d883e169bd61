package cn.cytong.vendor.distro.model.vo;


import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class FeeDistroListVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String distroId;
    private String ledgerId;
    private Date renewalTime;
    private String settleTime;

    private String productId;
    private String productName;

    private String ticketId;
    private String ticketNumber;

    private String cardId;
    private String cardNumber;

    private String vendorId;
    private String vendorLevel;

    private BigDecimal feeAmount;
    private String status;
    private String withdrawalRequestId;
    private Date createTime;
}
